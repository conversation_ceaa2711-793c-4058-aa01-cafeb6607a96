{% extends 'base.html' %}

{% block title %}CyberWolf - Scan Results{% endblock %}

{% block content %}
<div class="container">
    <!-- Loading Indicator (will be shown via JS if results aren't ready) -->
    <div id="loadingIndicator" class="row mb-4" style="display:none;">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h4>Scanning in progress...</h4>
                    <p class="text-muted">Please wait while we analyze the target for vulnerabilities.</p>
                    <p class="text-muted small">This page will automatically refresh when results are ready.</p>
                    <div class="progress mt-3" style="height: 10px;">
                        <div id="scanProgress" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" aria-valuenow="0" 
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section (will be hidden via JS if results aren't ready) -->
    <div id="resultsContainer">
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h2 class="h4 mb-0">Scan Results</h2>
                        <a href="{{ url_for('download_report') }}" class="btn btn-light btn-sm">
                            <i class="bi bi-download"></i> Download Report
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <p><strong>Target URL:</strong> {{ target_url }}</p>
                            <p><strong>Scan Completed:</strong> <span id="scanTime">{{ current_time }}</span></p>
                        </div>

                        <div class="alert alert-{{ 'danger' if results.summary.risk_level == 'High' else 'warning' if results.summary.risk_level == 'Medium' else 'success' }}">
                            <h4 class="alert-heading">Risk Level: {{ results.summary.risk_level }}</h4>
                            <p>Total vulnerabilities found: {{ results.summary.total }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- XSS Vulnerabilities Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header {{ 'bg-danger text-white' if results.xss else 'bg-success text-white' }}">
                    <h3 class="h5 mb-0">Cross-Site Scripting (XSS) Vulnerabilities</h3>
                </div>
                <div class="card-body">
                    {% if results.xss %}
                        <p class="mb-3">Found: {{ results.xss|length }} vulnerabilities</p>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Location</th>
                                        <th>Method</th>
                                        <th>Parameter</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for vuln in results.xss %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ vuln.location }}</td>
                                            <td>{{ vuln.method }}</td>
                                            <td>{{ vuln.param }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#xssModal{{ loop.index }}">
                                                    View Details
                                                </button>
                                            </td>
                                        </tr>
                                        
                                        <!-- Modal for XSS details -->
                                        <div class="modal fade" id="xssModal{{ loop.index }}" tabindex="-1">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger text-white">
                                                        <h5 class="modal-title">XSS Vulnerability #{{ loop.index }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <h6>Location:</h6>
                                                            <p>{{ vuln.location }}</p>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>Method:</h6>
                                                            <p>{{ vuln.method }}</p>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>Parameter:</h6>
                                                            <p>{{ vuln.param }}</p>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>Payload:</h6>
                                                            <pre class="bg-dark text-light p-3 rounded"><code>{{ vuln.payload }}</code></pre>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>URL:</h6>
                                                            <p class="text-break">{{ vuln.url }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success mb-0">
                            <i class="bi bi-shield-check"></i> No Cross-Site Scripting (XSS) vulnerabilities found.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- SQL Injection Vulnerabilities Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header {{ 'bg-danger text-white' if results.sqli else 'bg-success text-white' }}">
                    <h3 class="h5 mb-0">SQL Injection Vulnerabilities</h3>
                </div>
                <div class="card-body">
                    {% if results.sqli %}
                        <p class="mb-3">Found: {{ results.sqli|length }} vulnerabilities</p>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Location</th>
                                        <th>Method</th>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for vuln in results.sqli %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ vuln.location }}</td>
                                            <td>{{ vuln.method }}</td>
                                            <td>{{ vuln.param }}</td>
                                            <td>{{ vuln.error_type }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#sqliModal{{ loop.index }}">
                                                    View Details
                                                </button>
                                            </td>
                                        </tr>
                                        
                                        <!-- Modal for SQLi details -->
                                        <div class="modal fade" id="sqliModal{{ loop.index }}" tabindex="-1">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger text-white">
                                                        <h5 class="modal-title">SQLi Vulnerability #{{ loop.index }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <h6>Location:</h6>
                                                            <p>{{ vuln.location }}</p>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>Method:</h6>
                                                            <p>{{ vuln.method }}</p>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>Parameter:</h6>
                                                            <p>{{ vuln.param }}</p>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>Error Type:</h6>
                                                            <p>{{ vuln.error_type }}</p>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>Payload:</h6>
                                                            <pre class="bg-dark text-light p-3 rounded"><code>{{ vuln.payload }}</code></pre>
                                                        </div>
                                                        <div class="mb-3">
                                                            <h6>URL:</h6>
                                                            <p class="text-break">{{ vuln.url }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success mb-0">
                            <i class="bi bi-shield-check"></i> No SQL Injection vulnerabilities found.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Open Ports Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header {{ 'bg-warning text-dark' if results.ports else 'bg-success text-white' }}">
                    <h3 class="h5 mb-0">Open Ports and Services</h3>
                </div>
                <div class="card-body">
                    {% if results.ports %}
                        <p class="mb-3">Found: {{ results.ports|length }} open ports</p>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Port</th>
                                        <th>Protocol</th>
                                        <th>Service</th>
                                        <th>State</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for port in results.ports %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ port.port }}</td>
                                            <td>{{ port.protocol }}</td>
                                            <td>{{ port.service }}</td>
                                            <td>{{ port.state }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success mb-0">
                            <i class="bi bi-shield-check"></i> No open ports detected.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Vulnerable Paths Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header {{ 'bg-warning text-dark' if results.paths else 'bg-success text-white' }}">
                    <h3 class="h5 mb-0">Vulnerable Paths and Directories</h3>
                </div>
                <div class="card-body">
                    {% if results.paths %}
                        <p class="mb-3">Found: {{ results.paths|length }} vulnerable paths</p>
                        
                        <!-- High Risk Paths -->
                        {% set high_risk = results.paths|selectattr('risk_level', 'equalto', 'High')|list %}
                        {% if high_risk %}
                            <h6 class="text-danger mb-2">High Risk Paths</h6>
                            <div class="table-responsive mb-4">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Path</th>
                                            <th>Status</th>
                                            <th>Code</th>
                                            <th>URL</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for path in high_risk %}
                                            <tr>
                                                <td>{{ loop.index }}</td>
                                                <td class="text-danger">{{ path.path }}</td>
                                                <td>{{ path.status }}</td>
                                                <td>{{ path.status_code }}</td>
                                                <td class="text-break"><small>{{ path.full_url }}</small></td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% endif %}
                        
                        <!-- Medium Risk Paths -->
                        {% set medium_risk = results.paths|selectattr('risk_level', 'equalto', 'Medium')|list %}
                        {% if medium_risk %}
                            <h6 class="text-warning mb-2">Medium Risk Paths</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Path</th>
                                            <th>Status</th>
                                            <th>Code</th>
                                            <th>URL</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for path in medium_risk %}
                                            <tr>
                                                <td>{{ loop.index }}</td>
                                                <td class="text-warning">{{ path.path }}</td>
                                                <td>{{ path.status }}</td>
                                                <td>{{ path.status_code }}</td>
                                                <td class="text-break"><small>{{ path.full_url }}</small></td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-success mb-0">
                            <i class="bi bi-shield-check"></i> No vulnerable paths detected.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Remediation Recommendations -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h3 class="h5 mb-0">Remediation Recommendations</h3>
                </div>
                <div class="card-body">
                    {% if results.xss %}
                        <div class="mb-4">
                            <h6>For XSS Vulnerabilities:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">Implement proper input validation and sanitization</li>
                                <li class="list-group-item">Use Content-Security-Policy headers</li>
                                <li class="list-group-item">Apply output encoding when rendering user input</li>
                                <li class="list-group-item">Consider using frameworks with built-in XSS protection</li>
                            </ul>
                        </div>
                    {% endif %}
                    
                    {% if results.sqli %}
                        <div class="mb-4">
                            <h6>For SQL Injection Vulnerabilities:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">Use parameterized queries or prepared statements</li>
                                <li class="list-group-item">Implement proper input validation and sanitization</li>
                                <li class="list-group-item">Apply the principle of least privilege for database users</li>
                                <li class="list-group-item">Consider using ORM frameworks</li>
                            </ul>
                        </div>
                    {% endif %}
                    
                    {% if results.ports %}
                        <div class="mb-4">
                            <h6>For Open Ports:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">Close unnecessary ports and services</li>
                                <li class="list-group-item">Implement proper firewall rules</li>
                                <li class="list-group-item">Keep services updated to the latest secure versions</li>
                                <li class="list-group-item">Use network segmentation where possible</li>
                            </ul>
                        </div>
                    {% endif %}
                    
                    {% if results.paths %}
                        <div class="mb-4">
                            <h6>For Vulnerable Paths:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">Remove or secure sensitive files from web-accessible directories</li>
                                <li class="list-group-item">Implement proper access controls and authentication</li>
                                <li class="list-group-item">Configure web server to deny access to sensitive directories</li>
                                <li class="list-group-item">Use .htaccess or web.config files to restrict access</li>
                            </ul>
                        </div>
                    {% endif %}
                    
                    {% if not results.xss and not results.sqli and not results.ports and not results.paths %}
                        <div class="alert alert-success mb-0">
                            <i class="bi bi-shield-check"></i> No vulnerabilities found. Continue to maintain good security practices.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12 text-center">
            <a href="{{ url_for('scan') }}" class="btn btn-primary">Run Another Scan</a>
            <a href="{{ url_for('download_report') }}" class="btn btn-outline-secondary ms-2">Download Report</a>
        </div>
    </div>
</div> <!-- Close resultsContainer -->
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if scan is in progress or if results are complete
        let hasResults = {% if results.summary.total is defined %}true{% else %}false{% endif %};
        
        if (!hasResults) {
            // If no results yet, show loading indicator and hide results
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'none';
            
            // Update progress bar
            let progress = 0;
            let progressBar = document.getElementById('scanProgress');
            
            const progressInterval = setInterval(function() {
                if (progress < 95) {
                    progress += 5;
                    progressBar.style.width = progress + '%';
                    progressBar.setAttribute('aria-valuenow', progress);
                }
            }, 1000);
            
            // Auto-refresh the page every 5 seconds to check for results
            setTimeout(function() {
                location.reload();
            }, 5000);
        } else {
            // Results are ready, hide loading indicator (just in case)
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';
            
            // Update the scan time (if element exists)
            const scanTimeElement = document.getElementById('scanTime');
            if (scanTimeElement) {
                scanTimeElement.textContent = new Date().toLocaleString();
            }
        }
    });
</script>
{% endblock %}