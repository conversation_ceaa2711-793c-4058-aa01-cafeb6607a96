[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "androguard>=4.0.0",
    "asn1crypto>=1.5.1",
    "beautifulsoup4>=4.13.3",
    "dnspython>=2.7.0",
    "lxml>=5.3.0",
    "networkx>=3.4.2",
    "pandas>=2.2.3",
    "plotly>=6.0.0",
    "pycryptodome>=3.21.0",
    "pygments>=2.19.1",
    "python-whois>=0.9.5",
    "requests>=2.32.3",
    "rich>=13.9.4",
    "streamlit>=1.42.0",
    "trafilatura>=2.0.0",
    "urllib3>=2.3.0",
]
