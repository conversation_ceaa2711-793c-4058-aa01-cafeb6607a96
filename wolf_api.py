import os
import google.generativeai as genai
from typing import Dict, List, Any, Optional

class WolfAPI:
    """
    Wolf API , specialized for cybersecurity analysis.
    This is a rebranded interface to Gemini that focuses on security analysis capabilities.
    """
    
    def __init__(self, api_key: str = None):
        """
        Initialize the Wolf API client.
        
        Args:
            api_key (str, optional): The API key for authentication. If not provided,
                                     it will try to get it from environment variables.
        """
        # Use provided API key or try to get from environment
        self.api_key = api_key or os.getenv("WOLF_API_KEY") or os.getenv("GEMINI_API_KEY")
        
        if not self.api_key:
            raise ValueError("API key is required. Please provide it either directly or through environment variables.")
        
        # Configure the Gemini API with the provided key
        genai.configure(api_key=self.api_key)
        
        # Set default model to Gemini 2.0 Flash 
        self.default_model = "gemini-2.0-flash"
        
        # Initialize the model
        self.model = genai.GenerativeModel(self.default_model)
        
        # Define security-specific system prompts - enhanced for Gemini 2.0 Flash
        self.security_prompts = {
            "website_analysis": """You are Wolf API and are an elite cybersecurity specialist focusing on web security.
            You have been extensively trained on identifying and analyzing modern web security threats and vulnerabilities.
            Analyze the provided website information for security vulnerabilities, misconfigurations, and potential threats.
            Provide detailed, technical and actionable insights with severity ratings and specific remediation steps.""",
            
            "threat_detection": """You are Wolf API and are an elite cybersecurity specialist focusing on threat detection.
            You have been extensively trained on identifying malicious code patterns, security vulnerabilities, and threats in various programming languages.
            Analyze the provided content for security threats, vulnerabilities, and coding issues.
            Focus on identifying potential security risks and providing clear, actionable remediation steps with code examples where appropriate.""",
            
            "web_application_scan": """You are Wolf API and are an elite cybersecurity specialist focusing on web application security.
            You have deep expertise in OWASP Top 10 vulnerabilities, penetration testing techniques, and secure coding practices.
            Analyze the provided web application information for vulnerabilities, exposed endpoints, and security risks.
            Provide comprehensive vulnerability assessments with severity ratings, potential exploit scenarios, and specific remediation steps.""",
            
            "background_process": """You are Wolf API and are an elite cybersecurity specialist focusing on system security.
            You have extensive knowledge of secure configuration practices, privilege escalation techniques, and system hardening.
            Analyze the provided system, service, or process information for security misconfigurations, vulnerabilities, and threats.
            Focus on identifying potential security risks in background processes, services, and system configurations, with specific remediation guidance."""
        }
    
    def analyze_security(self, text: str, analysis_type: str, additional_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform security analysis on the provided text.
        
        Args:
            text (str): The text to analyze
            analysis_type (str): Type of security analysis to perform
            additional_context (Dict[str, Any], optional): Additional context for the analysis
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        # Get the appropriate system prompt
        system_prompt = self.security_prompts.get(
            analysis_type, 
            """You are Wolf API an  cybersecurity specialist. 
            You have comprehensive knowledge of security vulnerabilities, threats, and best practices.
            Analyze the provided information for security issues, vulnerabilities, and potential risks.
            Provide detailed technical analysis with clear remediation steps and recommendations."""
        )
        
        # Construct the prompt with additional context if provided
        context_str = ""
        if additional_context:
            context_str = "Additional context:\n"
            for key, value in additional_context.items():
                context_str += f"- {key}: {value}\n"
        
        # Create the full prompt
        full_prompt = f"{system_prompt}\n\n{context_str}\n\nContent to analyze:\n{text}\n\nProvide a detailed security analysis in JSON format with these sections: summary, security_score, vulnerabilities, recommendations, and technical_details."
        
        # Generate the response
        response = self.model.generate_content(full_prompt)
        
        # Process and return the response
        try:
            # Try to extract JSON from the response
            import json
            import re
            
            # Look for JSON pattern in the response
            response_text = response.text
            json_match = re.search(r'```json\n([\s\S]*?)\n```', response_text)
            
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
            else:
                # If no JSON pattern found, try to parse the entire response as JSON
                try:
                    return json.loads(response_text)
                except:
                    # If all parsing fails, return a structured response
                    return {
                        "summary": response_text[:500] + "...",
                        "security_score": 5,  # Default neutral score
                        "vulnerabilities": [],
                        "recommendations": [{"title": "Manual Review Required", "description": "The analysis requires manual review."}],
                        "technical_details": {"raw_response": response_text[:1000] + "..."}
                    }
        except Exception as e:
            # Handle errors in response processing
            return {
                "summary": "Error processing security analysis",
                "security_score": 0,
                "vulnerabilities": [{"severity": "High", "title": "Analysis Error", "description": f"Error processing response: {str(e)}"}],
                "recommendations": [{"title": "Retry Analysis", "description": "Please try again with more specific information."}],
                "technical_details": {"error": str(e), "partial_response": response.text[:500] + "..."}
            }
    
    def format_security_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format security data to ensure consistent structure.
        
        Args:
            data (Dict[str, Any]): Raw security data
            
        Returns:
            Dict[str, Any]: Formatted security data
        """
        # Ensure required fields exist
        required_fields = ["summary", "security_score", "vulnerabilities", "recommendations", "technical_details"]
        for field in required_fields:
            if field not in data:
                if field == "summary":
                    data[field] = "No summary provided"
                elif field == "security_score":
                    data[field] = 5  # Default neutral score
                elif field == "vulnerabilities":
                    data[field] = []
                elif field == "recommendations":
                    data[field] = []
                elif field == "technical_details":
                    data[field] = {}
        
        # Format vulnerabilities if they exist
        if data["vulnerabilities"]:
            for i, vuln in enumerate(data["vulnerabilities"]):
                if not isinstance(vuln, dict):
                    # Convert string to dict if needed
                    data["vulnerabilities"][i] = {
                        "severity": "Unknown",
                        "title": "Undefined Vulnerability",
                        "description": str(vuln),
                        "impact": "Unknown impact",
                        "component": "Unknown component"
                    }
                else:
                    # Ensure all vulnerability fields exist
                    if "severity" not in vuln:
                        vuln["severity"] = "Unknown"
                    if "title" not in vuln:
                        vuln["title"] = "Undefined Vulnerability"
                    if "description" not in vuln:
                        vuln["description"] = "No description provided"
                    if "impact" not in vuln:
                        vuln["impact"] = "Unknown impact"
                    if "component" not in vuln:
                        vuln["component"] = "Unknown component"
        
        # Format recommendations if they exist
        if data["recommendations"]:
            for i, rec in enumerate(data["recommendations"]):
                if not isinstance(rec, dict):
                    # Convert string to dict if needed
                    data["recommendations"][i] = {
                        "title": "Recommendation",
                        "description": str(rec)
                    }
                else:
                    # Ensure all recommendation fields exist
                    if "title" not in rec:
                        rec["title"] = "Recommendation"
                    if "description" not in rec:
                        rec["description"] = "No description provided"
        
        return data
