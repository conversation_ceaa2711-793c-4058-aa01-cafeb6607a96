<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CyberWolf - Automated Vulnerability Scanner{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
                <span class="me-2">🐺</span>
                <span>CyberWolf</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('index') %}active{% endif %}" href="{{ url_for('index') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('scan') %}active{% endif %}" href="{{ url_for('scan') }}">Scanner</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('api_keys') %}active{% endif %}" href="{{ url_for('api_keys') }}">API Keys</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('api_docs') %}active{% endif %}" href="{{ url_for('api_docs') }}">API Docs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('roadmap') %}active{% endif %}" href="{{ url_for('roadmap') }}">Roadmap</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('help_page') %}active{% endif %}" href="{{ url_for('help_page') }}">Help</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('about') %}active{% endif %}" href="{{ url_for('about') }}">About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2025 CyberWolf Team. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>Developed By CyberWolf Team</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>