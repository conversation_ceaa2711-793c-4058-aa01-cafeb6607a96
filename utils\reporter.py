"""
Vulnerability report generation module
"""

from datetime import datetime
from colorama import Fore, Style

def generate_report(target_url, results):
    """
    Generate a formatted vulnerability report
    
    Args:
        target_url (str): Target URL that was scanned
        results (dict): Scan results with vulnerability findings
    
    Returns:
        str: Formatted report text
    """
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Report header
    report = [
        f"{Fore.CYAN}=" * 80,
        f"CyberWolf Security Scan Report",
        f"Generated: {now}",
        f"Target: {target_url}",
        f"=" * 80,
        f"{Style.RESET_ALL}"
    ]
    
    # Executive summary
    total_vulns = sum(results['summary'].values())
    risk_level = results['summary'].get('risk_level', 'Unknown')
    risk_color = {
        'HIGH': Fore.RED,
        'MEDIUM': Fore.YELLOW,
        'LOW': Fore.GREEN,
        'MINIMAL': Fore.BLUE
    }.get(risk_level, Fore.WHITE)
    
    report.append(f"\n{Fore.WHITE}EXECUTIVE SUMMARY:{Style.RESET_ALL}")
    report.append(f"Total vulnerabilities found: {total_vulns}")
    report.append(f"Overall risk level: {risk_color}{risk_level}{Style.RESET_ALL}")
    report.append(f"{Fore.CYAN}{'-' * 80}{Style.RESET_ALL}")
    
    # XSS Vulnerabilities
    if results.get('xss'):
        report.append(f"\n{Fore.RED}CROSS-SITE SCRIPTING (XSS) VULNERABILITIES:{Style.RESET_ALL}")
        report.append(f"Found: {len(results['xss'])}")
        
        for i, vuln in enumerate(results['xss'], 1):
            report.append(f"\n  {i}. {Fore.YELLOW}Location: {vuln.get('location', 'Unknown')}{Style.RESET_ALL}")
            report.append(f"     Method: {vuln.get('method', 'GET')}")
            report.append(f"     Parameter: {vuln.get('param', 'N/A')}")
            report.append(f"     Payload: {vuln.get('payload', 'N/A')}")
            report.append(f"     URL: {vuln.get('url', 'N/A')}")
    else:
        report.append(f"\n{Fore.GREEN}NO CROSS-SITE SCRIPTING (XSS) VULNERABILITIES FOUND{Style.RESET_ALL}")
    
    # SQL Injection Vulnerabilities
    if results.get('sqli'):
        report.append(f"\n{Fore.RED}SQL INJECTION VULNERABILITIES:{Style.RESET_ALL}")
        report.append(f"Found: {len(results['sqli'])}")
        
        for i, vuln in enumerate(results['sqli'], 1):
            report.append(f"\n  {i}. {Fore.YELLOW}Location: {vuln.get('location', 'Unknown')}{Style.RESET_ALL}")
            report.append(f"     Method: {vuln.get('method', 'GET')}")
            report.append(f"     Parameter: {vuln.get('param', 'N/A')}")
            report.append(f"     Payload: {vuln.get('payload', 'N/A')}")
            report.append(f"     Type: {vuln.get('error_type', 'Unknown')}")
            report.append(f"     URL: {vuln.get('url', 'N/A')}")
    else:
        report.append(f"\n{Fore.GREEN}NO SQL INJECTION VULNERABILITIES FOUND{Style.RESET_ALL}")
    
    # Port Scan Results
    if results.get('ports'):
        report.append(f"\n{Fore.YELLOW}OPEN PORTS AND SERVICES:{Style.RESET_ALL}")
        report.append(f"Found: {len(results['ports'])}")
        
        for i, port in enumerate(results['ports'], 1):
            report.append(f"\n  {i}. {Fore.CYAN}Port: {port.get('port')}/{port.get('protocol', 'tcp')}{Style.RESET_ALL}")
            report.append(f"     Service: {port.get('service', 'Unknown')}")
            report.append(f"     State: {port.get('state', 'open')}")
    else:
        report.append(f"\n{Fore.GREEN}NO OPEN PORTS DETECTED{Style.RESET_ALL}")
    
    # Path Scan Results
    if results.get('paths'):
        report.append(f"\n{Fore.YELLOW}VULNERABLE PATHS AND DIRECTORIES:{Style.RESET_ALL}")
        report.append(f"Found: {len(results['paths'])}")
        
        # Group by risk level
        high_risk = [p for p in results['paths'] if p.get('risk_level') == 'High']
        medium_risk = [p for p in results['paths'] if p.get('risk_level') == 'Medium']
        
        if high_risk:
            report.append(f"\n  {Fore.RED}HIGH RISK PATHS:{Style.RESET_ALL}")
            for i, path in enumerate(high_risk, 1):
                report.append(f"  {i}. {Fore.RED}{path.get('path')}{Style.RESET_ALL}")
                report.append(f"     URL: {path.get('full_url')}")
                report.append(f"     Status: {path.get('status')} (Code: {path.get('status_code')})")
        
        if medium_risk:
            report.append(f"\n  {Fore.YELLOW}MEDIUM RISK PATHS:{Style.RESET_ALL}")
            for i, path in enumerate(medium_risk, 1):
                report.append(f"  {i}. {Fore.YELLOW}{path.get('path')}{Style.RESET_ALL}")
                report.append(f"     URL: {path.get('full_url')}")
                report.append(f"     Status: {path.get('status')} (Code: {path.get('status_code')})")
    else:
        report.append(f"\n{Fore.GREEN}NO VULNERABLE PATHS DETECTED{Style.RESET_ALL}")
    
    # Remediation recommendations
    report.append(f"\n{Fore.CYAN}REMEDIATION RECOMMENDATIONS:{Style.RESET_ALL}")
    
    if results.get('xss'):
        report.append(f"\n{Fore.WHITE}For XSS vulnerabilities:{Style.RESET_ALL}")
        report.append("  - Implement proper input validation and sanitization")
        report.append("  - Use Content-Security-Policy headers")
        report.append("  - Apply output encoding when rendering user input")
        report.append("  - Consider using frameworks with built-in XSS protection")
    
    if results.get('sqli'):
        report.append(f"\n{Fore.WHITE}For SQL Injection vulnerabilities:{Style.RESET_ALL}")
        report.append("  - Use parameterized queries or prepared statements")
        report.append("  - Implement proper input validation and sanitization")
        report.append("  - Apply the principle of least privilege for database users")
        report.append("  - Consider using ORM frameworks")
    
    if results.get('ports'):
        report.append(f"\n{Fore.WHITE}For open ports:{Style.RESET_ALL}")
        report.append("  - Close unnecessary ports and services")
        report.append("  - Implement proper firewall rules")
        report.append("  - Keep services updated to the latest secure versions")
        report.append("  - Use network segmentation where possible")
    
    if results.get('paths'):
        report.append(f"\n{Fore.WHITE}For vulnerable paths:{Style.RESET_ALL}")
        report.append("  - Remove or secure sensitive files from web-accessible directories")
        report.append("  - Implement proper access controls and authentication")
        report.append("  - Configure web server to deny access to sensitive directories")
        report.append("  - Use .htaccess or web.config files to restrict access")
    
    # Report footer
    report.append(f"\n{Fore.CYAN}{'=' * 80}")
    report.append(f"End of Report - The Developed By CyberWolf Team")
    report.append(f"{Fore.CYAN}{'=' * 80}{Style.RESET_ALL}\n")
    
    return "\n".join(report)
