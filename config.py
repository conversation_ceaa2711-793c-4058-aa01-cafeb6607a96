"""
Configuration settings for the CyberWolf scanner
"""

# Default timeout for requests (in seconds)
DEFAULT_TIMEOUT = 10

# Default User-Agent string
DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# Maximum number of threads to use for scanning
MAX_THREADS = 10

# Default output file name
DEFAULT_OUTPUT_FILE = "cyberwolf_report.txt"

# User preferences (can be modified by users)
PREFERENCES = {
    'verbose_output': False,
    'show_progress': True,
    'save_reports': True
}

# Scanner modules to enable by default
ENABLED_MODULES = {
    'xss_scanner': True,
    'sqli_scanner': True,
    'port_scanner': True,
    'path_scanner': True
}
