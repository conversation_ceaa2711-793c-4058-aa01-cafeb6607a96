<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberWolf API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .endpoint {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .method {
            font-weight: bold;
            padding: 3px 8px;
            border-radius: 3px;
            color: white;
        }
        .method.get { background-color: #28a745; }
        .method.post { background-color: #007bff; }
        .method.delete { background-color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">CyberWolf Security Scanner</a>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="mb-4">CyberWolf API Documentation</h1>
        
        <div class="alert alert-info">
            <strong>Base URL:</strong> http://localhost:5000/api
        </div>

        <h2 class="mt-4">Authentication</h2>
        <p>All API endpoints require an API key for authentication. Include your API key in the request header:</p>
        <pre><code>X-API-Key: your_api_key_here</code></pre>

        <h2 class="mt-4">Endpoints</h2>

        <div class="endpoint">
            <h3>Vulnerability Scan</h3>
            <p><span class="method post">POST</span> /scan</p>
            <p>Perform a vulnerability scan on a target URL.</p>
            
            <h4>Request Body</h4>
            <pre><code>{
    "url": "https://example.com",
    "timeout": 30,
    "attack_options": {
        "xss": true,
        "sqli": true,
        "ports": true,
        "paths": true
    }
}</code></pre>

            <h4>Response</h4>
            <pre><code>{
    "results": {
        "xss": [...],
        "sqli": [...],
        "ports": [...],
        "paths": [...],
        "summary": {
            "total_vulnerabilities": 5,
            "risk_level": "High"
        }
    }
}</code></pre>
        </div>

        <div class="endpoint">
            <h3>API Key Management</h3>
            <p><span class="method post">POST</span> /keys</p>
            <p>Generate a new API key.</p>
            
            <h4>Request Body</h4>
            <pre><code>{
    "name": "My API Key",
    "description": "Key for automated scanning"
}</code></pre>

            <h4>Response</h4>
            <pre><code>{
    "api_key": "generated_key_here",
    "key_info": {
        "name": "My API Key",
        "description": "Key for automated scanning",
        "created_at": "2024-01-01T00:00:00",
        "expires_at": "2024-02-01T00:00:00",
        "is_active": true
    }
}</code></pre>
        </div>

        <div class="endpoint">
            <h3>List API Keys</h3>
            <p><span class="method get">GET</span> /keys</p>
            <p>List all API keys (admin only).</p>
            
            <h4>Response</h4>
            <pre><code>{
    "keys": {
        "key1": {
            "name": "Key 1",
            "description": "Description",
            "created_at": "2024-01-01T00:00:00",
            "expires_at": "2024-02-01T00:00:00",
            "is_active": true
        }
    }
}</code></pre>
        </div>

        <div class="endpoint">
            <h3>Revoke API Key</h3>
            <p><span class="method delete">DELETE</span> /keys</p>
            <p>Revoke an API key.</p>
            
            <h4>Request Body</h4>
            <pre><code>{
    "api_key": "key_to_revoke"
}</code></pre>

            <h4>Response</h4>
            <pre><code>{
    "message": "API key revoked successfully"
}</code></pre>
        </div>
    </div>

    <footer class="bg-dark text-white mt-4 py-3">
        <div class="container text-center">
            <p>© 2025 CyberWolf Security Scanner. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>