# CyberWolf - Automated Vulnerability Scanner

CyberWolf is a Python-based CLI cybersecurity tool for automated vulnerability scanning and reporting developed by the CyberWolf Team.

## Features

- Vulnerability scanning of web applications and servers
- Detection of Cross-Site Scripting (XSS) vulnerabilities
- Detection of SQL Injection vulnerabilities
- Network port scanning and service identification
- Path and directory vulnerability detection
- Detailed reporting with remediation recommendations
- API integration for enhanced scanning (with API key)

## Requirements

- Python 3.x
- Required packages:
  - requests
  - beautifulsoup4
  - colorama
  - python-nmap (optional, for enhanced port scanning)

## Installation

1. Clone this repository:
   ```
   git clone https://github.com/cyberwolf-team/cyberwolf.git
   cd cyberwolf
   