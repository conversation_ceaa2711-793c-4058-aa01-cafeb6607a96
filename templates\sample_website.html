<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberWolf API Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .api-key {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .endpoint {
            color: #0d6efd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">CyberWolf API Demo</a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h2>API Key Information</h2>
                    </div>
                    <div class="card-body">
                        <p>Your API Key:</p>
                        <div class="api-key">eVGWe90JyjIH25K79S4OdF2vRAQvO2z5</div>
                        <p class="mt-3">Keep this key secure and do not share it publicly.</p>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h2>API Endpoints</h2>
                    </div>
                    <div class="card-body">
                        <h3>1. Vulnerability Scan</h3>
                        <p>Endpoint: <span class="endpoint">/api/scan</span></p>
                        <p>Method: POST</p>
                        <div class="code-block">
                            <pre><code>curl -X POST http://localhost:5000/api/scan \
-H "Content-Type: application/json" \
-H "X-API-Key: eVGWe90JyjIH25K79S4OdF2vRAQvO2z5" \
-d '{
    "url": "https://example.com",
    "timeout": 30,
    "attack_options": {
        "xss": true,
        "sqli": true,
        "ports": true,
        "paths": true
    }
}'</code></pre>
                        </div>

                        <h3 class="mt-4">2. API Key Management</h3>
                        <p>Endpoint: <span class="endpoint">/api/keys</span></p>
                        <p>Method: POST (Generate new key)</p>
                        <div class="code-block">
                            <pre><code>curl -X POST http://localhost:5000/api/keys \
-H "Content-Type: application/json" \
-H "X-Admin-API-Key: eVGWe90JyjIH25K79S4OdF2vRAQvO2z5" \
-d '{
    "name": "My Project Scanner",
    "description": "API key for project X",
    "expiry_days": 30
}'</code></pre>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h2>Example Response</h2>
                    </div>
                    <div class="card-body">
                        <h3>Scan Results</h3>
                        <div class="code-block">
                            <pre><code>{
    "results": {
        "xss": [
            {
                "type": "Reflected XSS",
                "endpoint": "/search.php",
                "parameter": "q",
                "evidence": "&lt;script&gt;alert(1)&lt;/script&gt;"
            }
        ],
        "sqli": [],
        "ports": [
            {
                "port": 80,
                "service": "http",
                "state": "open"
            }
        ],
        "paths": [
            {
                "path": "/admin",
                "status": 200,
                "risk_level": "High"
            }
        ],
        "summary": {
            "total_vulnerabilities": 2,
            "risk_level": "Medium"
        }
    }
}</code></pre>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h2>Security Best Practices</h2>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>Always use HTTPS for API requests</li>
                            <li>Store API keys securely (environment variables or secure vault)</li>
                            <li>Implement rate limiting in your applications</li>
                            <li>Regularly rotate API keys</li>
                            <li>Monitor API usage for suspicious activity</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white mt-4 py-3">
        <div class="container text-center">
            <p>© 2025 CyberWolf Security Scanner. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 