{% extends 'base.html' %}

{% block title %}CyberWolf - Scanner{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">Vulnerability Scanner</h2>
                </div>
                <div class="card-body">
                    <p class="mb-4">
                        Enter the target URL to scan for vulnerabilities. The scanner will check for XSS, SQL Injection, 
                        open ports, and vulnerable paths.
                    </p>
                    
                    <form action="{{ url_for('scan') }}" method="post" id="scanForm">
                        <div class="mb-3">
                            <label for="url" class="form-label">Target URL</label>
                            <input type="url" class="form-control" id="url" name="url" 
                                   placeholder="https://example.com" required>
                            <div class="form-text">Enter the full URL including http:// or https://</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Scan Type</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="scan_type" 
                                       id="basicScan" value="basic" checked>
                                <label class="form-check-label" for="basicScan">
                                    Basic Scan
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="scan_type" 
                                       id="apiScan" value="api">
                                <label class="form-check-label" for="apiScan">
                                    API Enhanced Scan
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3 api-key-section d-none">
                            <label for="apiKey" class="form-label">API Key</label>
                            <input type="text" class="form-control" id="apiKey" name="api_key" 
                                   placeholder="Enter your API key">
                            <div class="form-text">
                                API Enhanced scanning provides more comprehensive vulnerability detection.
                                Don't have an API key? <a href="{{ url_for('api_keys') }}">Generate one here</a>.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Attack Options</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="attack_xss" 
                                               id="attackXSS" value="1" checked>
                                        <label class="form-check-label" for="attackXSS">
                                            XSS (Cross-Site Scripting)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="attack_sqli" 
                                               id="attackSQLi" value="1" checked>
                                        <label class="form-check-label" for="attackSQLi">
                                            SQL Injection
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="attack_ports" 
                                               id="attackPorts" value="1" checked>
                                        <label class="form-check-label" for="attackPorts">
                                            Port Scanning
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="attack_paths" 
                                               id="attackPaths" value="1" checked>
                                        <label class="form-check-label" for="attackPaths">
                                            Path Traversal
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="scanTimeout" class="form-label">Scan Timeout (seconds)</label>
                            <input type="number" class="form-control" id="scanTimeout" name="timeout" 
                                   value="10" min="5" max="60">
                            <div class="form-text">
                                Longer timeouts allow for more thorough scanning but take more time.
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="scanButton">
                                <span class="spinner-border spinner-border-sm d-none" role="status" id="scanSpinner"></span>
                                <span id="scanButtonText">Start Scan</span>
                            </button>
                        </div>
                        
                        <div class="mt-3 text-center">
                            <p class="text-muted small" id="scanningMessage" style="display:none;">
                                <i class="spinner-border spinner-border-sm"></i>
                                Scanning in progress... This may take up to a minute depending on the target.
                                Please wait while the system analyzes vulnerabilities.
                            </p>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card mt-4 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h3 class="h5 mb-0">Scanner Options</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h4 class="h6">What we scan for:</h4>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">Cross-Site Scripting (XSS) vulnerabilities</li>
                            <li class="list-group-item">SQL Injection vulnerabilities</li>
                            <li class="list-group-item">Open ports and services</li>
                            <li class="list-group-item">Vulnerable paths and directories</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <h5 class="h6">Important Notes:</h5>
                        <ul class="mb-0">
                            <li>Only scan websites you have permission to test.</li>
                            <li>Scanning may take several minutes depending on the target.</li>
                            <li>For comprehensive results, use API Enhanced scanning.</li>
                            <li>Need programmatic access? Check our <a href="{{ url_for('api_docs') }}" class="alert-link">API documentation</a>.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle API key input based on scan type
        const apiScanRadio = document.getElementById('apiScan');
        const basicScanRadio = document.getElementById('basicScan');
        const apiKeySection = document.querySelector('.api-key-section');
        
        apiScanRadio.addEventListener('change', function() {
            if (this.checked) {
                apiKeySection.classList.remove('d-none');
            }
        });
        
        basicScanRadio.addEventListener('change', function() {
            if (this.checked) {
                apiKeySection.classList.add('d-none');
            }
        });
        
        // Show loading state when form is submitted
        const scanForm = document.getElementById('scanForm');
        const scanButton = document.getElementById('scanButton');
        const scanSpinner = document.getElementById('scanSpinner');
        const scanButtonText = document.getElementById('scanButtonText');
        const scanningMessage = document.getElementById('scanningMessage');
        
        scanForm.addEventListener('submit', function() {
            // Change button appearance
            scanButtonText.textContent = 'Scanning...';
            scanSpinner.classList.remove('d-none');
            scanButton.disabled = true;
            
            // Show scanning message
            scanningMessage.style.display = 'block';
            
            // Make sure form submits even if there's a delay
            setTimeout(function() {
                if (scanButton.disabled) {
                    scanForm.submit();
                }
            }, 10000); // Force submit after 10 seconds if not already submitted
        });
    });
</script>
{% endblock %}