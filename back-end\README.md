
# Security Analysis Tool

A modular security analysis framework for bug finding and vulnerability assessment.

## Features

- Security vulnerability scanning
- APK analysis
- Website cloning and analysis
- SSL certificate analysis
- Subdomain enumeration
- IP analysis
- Footprint analysis
- URL path scanning

## Setup

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Run the security analyzer:
```bash
python security_analyzer.py -d example.com --verbose
```

3. Run the web interface:
```bash
streamlit run streamlit_app.py --server.port 840
```

## Modules

- APK Security Analysis
- Footprint Analysis
- IP Analysis
- Website Structure Analysis
- SSL Certificate Analysis
- Subdomain Enumeration
- URL Path Scanner
