{% extends 'base.html' %}

{% block title %}CyberWolf - About{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-5">
        <div class="col-lg-12 text-center">
            <h1 class="display-4 mb-4">About CyberWolf</h1>
            <div class="ascii-art text-primary mb-4 d-flex justify-content-center">
<pre>
                    ,     ,
                    |\---/|
                   /       \
                  |         |
                   \       /
                    ||   ||
                    ||   ||
                    '---'
</pre>
            </div>
            <p class="lead">
                A comprehensive automated vulnerability scanner developed by the CyberWolf Team.
            </p>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">Our Mission</h2>
                </div>
                <div class="card-body">
                    <p>
                        CyberWolf was created with a mission to democratize web security testing and make it 
                        accessible to everyone. We believe that security should not be an afterthought or a 
                        luxury, but a fundamental aspect of every web application.
                    </p>
                    <p>
                        Our goal is to provide a powerful yet easy-to-use tool that helps developers and 
                        system administrators identify and fix security vulnerabilities before they can be 
                        exploited by malicious actors.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">Key Features</h2>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <strong>Comprehensive Scanning:</strong> Detect XSS, SQL Injection, open ports, and vulnerable paths
                        </li>
                        <li class="list-group-item">
                            <strong>Dual Interface:</strong> Choose between web UI or command-line interface
                        </li>
                        <li class="list-group-item">
                            <strong>Detailed Reporting:</strong> Get actionable insights with clear remediation steps
                        </li>
                        <li class="list-group-item">
                            <strong>API Enhanced Scanning:</strong> Leverage advanced APIs for deeper vulnerability detection
                        </li>
                        <li class="list-group-item">
                            <strong>User-Friendly Design:</strong> Simple to use without sacrificing power
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-lg-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">About the CyberWolf Team</h2>
                </div>
                <div class="card-body">
                    <p>
                        The CyberWolf Team is a group of security enthusiasts dedicated to improving the security 
                        posture of web applications through education, tool development, and research.
                    </p>
                    <p>
                        Our team brings together expertise in web security, penetration testing, software 
                        development, and user experience design to create tools that are both powerful and 
                        accessible.
                    </p>
                    <p>
                        We believe in the importance of responsible disclosure and ethical security testing. 
                        All our tools, including CyberWolf, are designed to be used for legitimate security 
                        testing with proper authorization.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">Project History</h2>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item pb-3">
                            <h5 class="h6">2025</h5>
                            <p>
                                CyberWolf 1.0 released with comprehensive web UI and enhanced scanning capabilities.
                            </p>
                        </div>
                        <div class="timeline-item pb-3">
                            <h5 class="h6">2024</h5>
                            <p>
                                Development of the CyberWolf project began as a CLI-based vulnerability scanner.
                            </p>
                        </div>
                        <div class="timeline-item">
                            <h5 class="h6">2023</h5>
                            <p>
                                Initial research and concept development for automated vulnerability scanning tools.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">Technology Stack</h2>
                </div>
                <div class="card-body">
                    <p>CyberWolf is built using a modern technology stack:</p>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <strong>Python:</strong> Core application logic and scanning modules
                        </li>
                        <li class="list-group-item">
                            <strong>Flask:</strong> Web interface framework
                        </li>
                        <li class="list-group-item">
                            <strong>Requests & BeautifulSoup:</strong> HTTP handling and HTML parsing
                        </li>
                        <li class="list-group-item">
                            <strong>Python-nmap:</strong> Port scanning capabilities
                        </li>
                        <li class="list-group-item">
                            <strong>Colorama:</strong> CLI terminal text formatting
                        </li>
                        <li class="list-group-item">
                            <strong>Bootstrap:</strong> Responsive UI design
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-lg-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">Responsible Use</h2>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Important Security Notice</h5>
                        <p>
                            CyberWolf is a powerful security testing tool that should be used responsibly and ethically. Please remember:
                        </p>
                        <ul class="mb-0">
                            <li>Only scan websites you own or have explicit permission to test</li>
                            <li>Unauthorized security testing may be illegal in many jurisdictions</li>
                            <li>Report vulnerabilities responsibly to website owners</li>
                            <li>Use this tool for educational and legitimate security testing purposes only</li>
                        </ul>
                    </div>
                    <p>
                        The CyberWolf Team and developers are not responsible for any misuse of this tool. By using 
                        CyberWolf, you acknowledge that you understand these terms and agree to use the tool responsibly.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-lg-12 text-center">
            <h3 class="mb-4">Ready to improve your web security?</h3>
            <a href="{{ url_for('scan') }}" class="btn btn-primary btn-lg px-5">Start Scanning</a>
        </div>
    </div>
</div>
{% endblock %}