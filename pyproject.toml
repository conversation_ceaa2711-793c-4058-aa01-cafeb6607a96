[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "colorama>=0.4.6",
    "email-validator>=2.2.0",
    "flask>=3.1.0",
    "flask-sqlalchemy>=3.1.1",
    "gunicorn>=23.0.0",
    "psycopg2-binary>=2.9.10",
    "python-nmap>=0.7.1",
    "requests>=2.32.3",
    "trafilatura>=2.0.0",
]
