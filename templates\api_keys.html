{% extends 'base.html' %}

{% block title %}CyberWolf - API Keys Management{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="h4 mb-0">API Keys Management</h2>
                </div>
                <div class="card-body">
                    <p class="mb-4">
                        Generate and manage API keys for enhanced vulnerability scanning capabilities.
                        API keys enable access to more advanced scanning features and detailed vulnerability detection.
                        <a href="{{ url_for('api_docs') }}" class="text-decoration-underline">View API documentation</a> for more information.
                    </p>
                    
                    <form action="{{ url_for('api_keys') }}" method="post" class="mb-4">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-8">
                                <label for="keyName" class="form-label">API Key Name</label>
                                <input type="text" class="form-control" id="keyName" name="key_name" 
                                       placeholder="Enter a descriptive name (e.g., Project-X Scanner)" required>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100">Generate New API Key</button>
                            </div>
                        </div>
                    </form>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> <strong>Important:</strong> 
                        Save your API key securely. For security reasons, the complete key is only shown once upon generation.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h3 class="h5 mb-0">Your API Keys</h3>
                </div>
                <div class="card-body">
                    {% if keys %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Key Name</th>
                                        <th>Key</th>
                                        <th>Created</th>
                                        <th>Last Used</th>
                                        <th>Usage Count</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for key_data in keys %}
                                        <tr>
                                            <td>{{ key_data.name }}</td>
                                            <td>
                                                <span class="text-muted">{{ key_data.key[:8] }}...{{ key_data.key[-4:] }}</span>
                                                <button class="btn btn-sm btn-outline-secondary ms-2 copy-key"
                                                        data-key="{{ key_data.key }}" type="button">
                                                    Copy
                                                </button>
                                            </td>
                                            <td>{{ key_data.created_at }}</td>
                                            <td>{{ key_data.last_used_at or 'Never' }}</td>
                                            <td>{{ key_data.usage_count }}</td>
                                            <td>
                                                {% if key_data.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Revoked</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if key_data.is_active %}
                                                    <form action="{{ url_for('revoke_api_key', key=key_data.key) }}" method="post" class="d-inline">
                                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                                onclick="return confirm('Are you sure you want to revoke this API key? This cannot be undone.')">
                                                            Revoke
                                                        </button>
                                                    </form>
                                                {% else %}
                                                    <button class="btn btn-sm btn-secondary" disabled>Revoked</button>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-secondary mb-0">
                            <p class="mb-0">You haven't generated any API keys yet. Use the form above to create your first key.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h3 class="h5 mb-0">Using API Keys</h3>
                </div>
                <div class="card-body">
                    <h4 class="h6">Web Interface:</h4>
                    <p>
                        To use an API key in the web interface, select "API Enhanced Scan" on the scanner page 
                        and enter your API key in the field that appears.
                    </p>
                    
                    <h4 class="h6">Command Line Interface:</h4>
                    <p>To use an API key with the CLI tool, you can either:</p>
                    <ul>
                        <li>Select "API Enhanced Scan" from the menu and enter your key when prompted</li>
                        <li>
                            Use the command-line argument:
                            <pre><code>python cyberwolf.py --url https://example.com --api-key YOUR_API_KEY</code></pre>
                        </li>
                    </ul>
                    
                    <h4 class="h6">API Access:</h4>
                    <p>For programmatic access, include your API key in the request headers:</p>
                    <pre><code>
curl -X POST https://cyberwolf.example.com/api/scan \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_API_KEY" \
  -d '{"url": "https://example.com"}'
                    </code></pre>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Copy API key to clipboard
    document.querySelectorAll('.copy-key').forEach(button => {
        button.addEventListener('click', function() {
            const key = this.getAttribute('data-key');
            navigator.clipboard.writeText(key).then(() => {
                const originalText = this.textContent;
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
            });
        });
    });
</script>
{% endblock %}